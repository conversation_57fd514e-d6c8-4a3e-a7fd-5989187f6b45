#include <iostream>
#include "supportFunc.h"
#include "stackController.h"
#include "rollingScheduler.h"
using namespace std;

// 创建轮值调度器实例
static RollingScheduler scheduler;

StackControl::StackControl() {
    // 初始化电解槽运行和停机时间
    for (int g = 0; g < NUM_GROUP; g++) {
        for (int s = 0; s < NUM_STACK; s++) {
            Stack_Runtime[g][s] = 0.0f;
            Stack_Stoptime[g][s] = 0.0f;
        }
    }
    enable_rolling_schedule = false;
}

// 新增：更新电解槽轮值调度
void StackControl::UpdateRollingSchedule(const double* in_power) {
    if (!enable_rolling_schedule) return;
    
    // 更新电解槽运行时间和停机时间（将秒转换为小时）
    scheduler.UpdateRunningTime(Set_Flag, delta_t / 3600.0f);
    
    // 更新电解槽的启停状态
    bool new_set_flag[NUM_GROUP][NUM_STACK];
    for (int g = 0; g < NUM_GROUP; g++) {
        for (int s = 0; s < NUM_STACK; s++) {
            new_set_flag[g][s] = Set_Flag[g][s];
        }
    }
    
    // 根据总功率需求更新电解槽调度
    scheduler.UpdateStackSchedule(new_set_flag, *in_power, pow_rated);
    
    // 应用新的启停状态
    for (int g = 0; g < NUM_GROUP; g++) {
        for (int s = 0; s < NUM_STACK; s++) {
            Set_Flag[g][s] = new_set_flag[g][s];
            
            // 更新运行时间和停机时间记录
            Stack_Runtime[g][s] = scheduler.GetStackRuntime(g, s);
            Stack_Stoptime[g][s] = scheduler.GetStackStoptime(g, s);
        }
    }
}

// 新增：设置轮值调度参数
void StackControl::SetRollingScheduleParams(bool enable, float max_runtime_hours, float min_stoptime_hours) {
    enable_rolling_schedule = enable;
    
    // 更新轮值调度器的阈值参数
    // 注意：这里需要修改 rollingScheduler.h 添加相应的方法
    // 或者直接在 UpdateRollingSchedule 中传递这些参数
}

// 新增：获取电解槽运行时间
float StackControl::GetStackRuntime(int group, int stack) const {
    return Stack_Runtime[group][stack];
}

// 新增：获取电解槽停机时间
float StackControl::GetStackStoptime(int group, int stack) const {
    return Stack_Stoptime[group][stack];
}

	void StackControl::InputModule(const double *in_tem_iye, const int *in_set_flag, const double *in_pow_feedback) {
		for (int g = 0; g < NUM_GROUP; g++) {
			for (int s = 0; s < NUM_STACK; s++) {
				//Stack_State[g][s] = in[ g * NUM_STACK + s];
		/*		Tem_Iye[g][s] = in_tem_iye[g * NUM_STACK + s];
				Set_Flag[g][s] = in_set_flag[g * NUM_STACK + s];*/
				//Pow_Feedback[g][s] = in_pow_feedback[g * NUM_STACK + s];
				Tem_Iye[g][s] = in_tem_iye[control_index[g][s]];
				Set_Flag[g][s] = in_set_flag[control_index[g][s]];
				Pow_Feedback[g][s] = in_pow_feedback[control_index[g][s]];

			}
		}
	}

	void StackControl::UpdatePowSetLast(double* out_pow_set, double* xD) {
		for (int g = 0; g < NUM_GROUP; g++) {
			for (int s = 0; s < NUM_STACK; s++) {
				xD[g * NUM_STACK + s] = out_pow_set[g * NUM_STACK + s];

			}
		}
	}

	void StackControl::UpdatePowerAllocation(const double *in_power, const double* xD, double *out_pow_set, 
		double *out_temp, double *var1, double *var2, double *var3, double *var4) {
		int N = NUM_GROUP * NUM_STACK;		// total number of available elyzers
		float pow_toele;
		float out_pow_temp[NUM_GROUP * NUM_STACK];
		float pow_remain;

		pow_remain = *in_power;
		// allocate elyzer power
		for (int g = 0; g < NUM_GROUP; g++) {			
			for (int s = 0; s < NUM_STACK; s++) {
				Pow_Set_Last[g][s] = xD[control_index[g][s]];
				pow_toele = pow_remain;
				float temp;

				float Pelemin = Pow_Feedback[g][s] - beta_down[g][s] * delta_t;
				float Pelemax = Pow_Feedback[g][s] + beta_up[g][s] * delta_t;
				float PeleSetmin = Pow_Set_Last[g][s] - beta_down[g][s] * delta_t;
				float PeleSetmax = Pow_Set_Last[g][s] + beta_up[g][s] * delta_t;

				if (Set_Flag[g][s] == 1) {				// if the elyzer is available 
					if (pow_toele <= N * pow_mid) {
						if (pow_toele >= 2 * pow_mid) {
							temp = pow_mid;
						}
						else {
							temp = pow_toele;
						}
					}
					else {
						temp = pow_toele / N;
					}
					temp = limit(temp, 0, pow_max);
					var1[g * NUM_STACK + s] = temp;
					//temp = limit(temp, Pelemin, Pelemax);		// ����ֵб������
					var2[g * NUM_STACK + s] = temp;
					temp = limit(temp, PeleSetmin, PeleSetmax);	// �趨ֵб������
					var3[g * NUM_STACK + s] = temp;
				}
				else {
					temp = 0;
				}

				Pow_Set[g][s] = temp;
				out_pow_set[control_index[g][s]] = Pow_Set[g][s];
				out_temp[g * NUM_STACK + s] = xD[g * NUM_STACK + s];

				pow_remain = pow_remain - Pow_Set[g][s];
				var4[g * NUM_STACK + s] = pow_remain;
				N = N - 1;

			}
		}
	}

	void StackControl::UpdateStackState(int* out_stack_state) {
		for (int g = 0; g < NUM_GROUP; g++) {
			for (int s = 0; s < NUM_STACK; s++) {
				if (Set_Flag[g][s]) {
					if (Tem_Iye[g][s] < 60) {
						if (Pow_Set[g][s] ==0){
							Stack_State[g][s] = 1;
						}
						else if(Pow_Set[g][s]< pow_min){
							Stack_State[g][s] = 2;
						}
						else if(Pow_Set[g][s]<= pow_rated){
							Stack_State[g][s] = 3;
						}
						else{
							Stack_State[g][s] = 4;
						}
					}
					else {
						if (Pow_Set[g][s] == 0) {
							Stack_State[g][s] = 5;
						}
						else if (Pow_Set[g][s] < pow_min) {
							Stack_State[g][s] = 6;
						}
						else if (Pow_Set[g][s] <= pow_rated) {
							Stack_State[g][s] = 7;
						}
						else {
							Stack_State[g][s] = 8;
						}
					}
				}
				else {
					Stack_State[g][s] = 0;
				}
				out_stack_state[g * NUM_STACK + s] = Stack_State[g][s];
			}
		}
	}


	void StackControl::UpdateHealth() {
		int control_index[NUM_GROUP][NUM_STACK] = {
			{ 0, 1, 2, 4 },
			{ 5, 6, 3, 7 },
		};
	}
