#pragma once
#include <iostream>
#include <vector>

class StackControl {

public:

	static const int NUM_GROUP = 2;
	static const int NUM_STACK = 4;
	int control_index[NUM_GROUP][NUM_STACK] = {
		{0, 1, 2, 4},
		{5, 6, 3, 7},
	};
	float delta_t = 1;	//控制步长，s
	float pow_rated = 5000;
	float pow_max = 5000;
	float pow_min = 1500;
	float pow_mid = 2500;
	float beta_down[NUM_GROUP][NUM_STACK] = {
		{ 100, 100, 100, 100 },
		{ 100, 100, 100, 100 },
	};
	float beta_up[NUM_GROUP][NUM_STACK] = {
		{ 100, 100, 100, 100 },
		{ 100, 100, 100, 100 },
	};
	// State for all stacks
	int Stack_State[NUM_GROUP][NUM_STACK];
	// Temperature for all stacks
	float Tem_Iye[NUM_GROUP][NUM_STACK];
	// Controllable flag for all stacks
	bool Set_Flag[NUM_GROUP][NUM_STACK];
	// Feed back power for all stacks
	float Pow_Feedback[NUM_GROUP][NUM_STACK];
	// Power set value for all stacks
	float Pow_Set[NUM_GROUP][NUM_STACK];
	// Power set value with out speed limit for all stacks
	float Pow_Set_Temp[NUM_GROUP][NUM_STACK];
	// Power set for last time step
	float Pow_Set_Last[NUM_GROUP][NUM_STACK];
	
	// 电解槽运行时间记录(小时)
	float Stack_Runtime[NUM_GROUP][NUM_STACK];
	// 电解槽停机时间记录(小时)
	float Stack_Stoptime[NUM_GROUP][NUM_STACK];
	// 是否启用轮值调度
	bool enable_rolling_schedule;

public:
	StackControl();

	void InputModule(const double* in_tem_iye, const int* in_set_flag, const double* in_pow_feedback);

	void UpdatePowSetLast(double* out_pow_set, double* xD);

	void UpdatePowerAllocation(const double* in_power, const double* xD, double* out_pow_set, double* out_temp, double *var1, double *var2, double *var3, double *var4);

	void UpdateStackState(int* out_stack_state);

	void UpdateHealth();
	
	// 新增：更新电解槽轮值调度
	void UpdateRollingSchedule(const double* in_power);
	
	// 新增：设置轮值调度参数
	void SetRollingScheduleParams(bool enable, float max_runtime_hours = 168.0f, float min_stoptime_hours = 24.0f);
	
	// 新增：获取电解槽运行时间
	float GetStackRuntime(int group, int stack) const;
	
	// 新增：获取电解槽停机时间
	float GetStackStoptime(int group, int stack) const;
};




